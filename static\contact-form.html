<!DOCTYPE html>
<html>
<head>
    <title>Contact Form - Hidden for Netlify</title>
</head>
<body>
    <!-- 
    This is a hidden HTML form that Netlify can parse at build time.
    It must match the structure of the JavaScript-rendered form in src/components/contact-form.tsx
    This file is required for Netlify Forms to work with React/JavaScript-rendered forms.
    -->
    <form 
        name="contact" 
        method="POST" 
        data-netlify="true" 
        data-netlify-honeypot="bot-field"
        style="display: none;"
    >
        <!-- Hidden field for form identification -->
        <input type="hidden" name="form-name" value="contact" />
        
        <!-- Honeypot field for spam protection -->
        <div style="display: none;">
            <label>Don't fill this out if you're human: <input name="bot-field" /></label>
        </div>

        <!-- Form fields that match the React component -->
        <label>Nom: <input type="text" name="name" required /></label>
        <label>Email: <input type="email" name="email" required /></label>
        <label>Sujet: <input type="text" name="subject" required /></label>
        <label>Message: <textarea name="message" required></textarea></label>
        
        <button type="submit">Envoyer le message</button>
    </form>
</body>
</html>
